"""Business profile management and interaction tracking."""
import logging
import re
from typing import Optional, List, Dict, Any
from datetime import datetime
from urllib.parse import urlparse

from core.models import EmailData
from database.models import User, BusinessEntity, UserBusinessInteraction, BusinessPrivacyPolicy, PrivacyDataPractice
from database.manager import get_db_manager
from config.logging_config import LoggerMixin, log_execution_time
from utils.exceptions import BusinessManagerError, DatabaseError, handle_exception
from .privacy_scraper import PrivacyPolicyScraper, PrivacyPolicyData
from agents.business_agent import BusinessIdentificationAgent

logger = logging.getLogger(__name__)

class BusinessProfileManager(LoggerMixin):
    """Manages business profiles and user-business interactions."""

    def __init__(self, enable_privacy_scraping: bool = True, enable_agent_identification: bool = True):
        """Initialize the business profile manager.

        Args:
            enable_privacy_scraping: Whether to enable automatic privacy policy scraping
            enable_agent_identification: Whether to enable <PERSON><PERSON><PERSON><PERSON> agent for business identification
        """
        self.db = get_db_manager()
        self.enable_privacy_scraping = enable_privacy_scraping
        self.enable_agent_identification = enable_agent_identification
        self.privacy_scraper = PrivacyPolicyScraper() if enable_privacy_scraping else None
        self.business_agent = BusinessIdentificationAgent() if enable_agent_identification else None
        self.logger.info(f"BusinessProfileManager initialized (privacy_scraping={enable_privacy_scraping}, agent_identification={enable_agent_identification})")
    
    def extract_domain_from_email(self, email: str) -> Optional[str]:
        """Extract domain from email address.
        
        Args:
            email: Email address to extract domain from
            
        Returns:
            Domain name or None if extraction fails
        """
        if not email or '@' not in email:
            return None
        
        try:
            domain = email.split('@')[1].lower().strip()
            
            # Remove common subdomains
            if domain.startswith('mail.'):
                domain = domain[5:]
            elif domain.startswith('smtp.'):
                domain = domain[5:]
            elif domain.startswith('noreply.'):
                domain = domain[8:]
            
            # Validate domain format
            if '.' not in domain or len(domain) < 3:
                return None
                
            return domain
            
        except (IndexError, AttributeError) as e:
            self.logger.warning(f"Error extracting domain from email '{email}': {e}")
            return None
    
    def extract_domain_from_url(self, url: str) -> Optional[str]:
        """Extract domain from URL.
        
        Args:
            url: URL to extract domain from
            
        Returns:
            Domain name or None if extraction fails
        """
        if not url:
            return None
        
        try:
            # Add protocol if missing
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            parsed = urlparse(url)
            domain = parsed.netloc.lower()
            
            # Remove www prefix
            if domain.startswith('www.'):
                domain = domain[4:]
            
            # Validate domain
            if '.' not in domain or len(domain) < 3:
                return None
                
            return domain
            
        except Exception as e:
            self.logger.warning(f"Error extracting domain from URL '{url}': {e}")
            return None
    
    def normalize_business_name(self, name: str) -> str:
        """Normalize business name for consistent storage.
        
        Args:
            name: Business name to normalize
            
        Returns:
            Normalized business name
        """
        if not name:
            return ""
        
        try:
            # Remove extra whitespace and normalize
            name = ' '.join(name.strip().split())
            
            # Remove email-like patterns
            if '@' in name:
                name = name.split('@')[0]
            
            # Remove common business suffixes for normalization
            suffixes = [
                r'\s+(inc\.?|incorporated)$',
                r'\s+(llc\.?)$',
                r'\s+(ltd\.?|limited)$',
                r'\s+(corp\.?|corporation)$',
                r'\s+(co\.?)$',
                r'\s+company$',
                r'\s+(group|grp)$',
                r'\s+(enterprises?|ent)$'
            ]
            
            for suffix in suffixes:
                name = re.sub(suffix, '', name, flags=re.IGNORECASE)
            
            # Clean up and title case
            name = name.strip()
            if name:
                # Only title case if it's all uppercase or all lowercase
                if name.isupper() or name.islower():
                    name = name.title()
            
            return name
            
        except Exception as e:
            self.logger.warning(f"Error normalizing business name '{name}': {e}")
            return str(name).strip()
    
    def infer_industry_from_domain(self, domain: str) -> Optional[str]:
        """Infer industry from domain name using heuristics.
        
        Args:
            domain: Domain name to analyze
            
        Returns:
            Industry name or 'other' if no match found
        """
        if not domain:
            return None
        
        domain_lower = domain.lower()
        
        # Common industry patterns
        industry_patterns = {
            'technology': ['tech', 'software', 'app', 'digital', 'cloud', 'ai', 'data', 'dev', 'code'],
            'finance': ['bank', 'finance', 'invest', 'credit', 'loan', 'pay', 'wallet', 'capital'],
            'retail': ['shop', 'store', 'retail', 'market', 'buy', 'sell', 'commerce'],
            'healthcare': ['health', 'medical', 'care', 'hospital', 'clinic', 'pharma', 'med'],
            'education': ['edu', 'school', 'university', 'college', 'learn', 'academy'],
            'media': ['news', 'media', 'blog', 'press', 'journal', 'magazine'],
            'travel': ['travel', 'hotel', 'flight', 'booking', 'trip', 'vacation'],
            'food': ['food', 'restaurant', 'delivery', 'kitchen', 'recipe', 'dining'],
            'real_estate': ['real', 'estate', 'property', 'rent', 'home', 'realty'],
            'automotive': ['auto', 'car', 'vehicle', 'drive', 'motor', 'automotive'],
            'entertainment': ['game', 'gaming', 'entertainment', 'music', 'video', 'stream'],
            'consulting': ['consulting', 'advisory', 'services', 'solutions']
        }
        
        for industry, keywords in industry_patterns.items():
            if any(keyword in domain_lower for keyword in keywords):
                return industry
        
        return 'other'

    def _create_business_data_from_agent_result(self, agent_result: Dict[str, Any],
                                              email_data: EmailData,
                                              normalized_name: str) -> Dict[str, Any]:
        """Create business data dictionary from agent identification result.

        Args:
            agent_result: Result from business identification agent
            email_data: Original email data
            normalized_name: Normalized business name

        Returns:
            Dictionary containing business entity data
        """
        # Extract domain from email
        domain = self.extract_domain_from_email(email_data.from_field)

        # Start with basic data
        business_data = {
            'domain': domain,
            'contact_email': email_data.from_field,
            'description': f"Business entity identified from email communication using AI agent"
        }

        # Enhance with agent results
        if agent_result.get('website_url'):
            business_data['website'] = agent_result['website_url']

        if agent_result.get('business_type'):
            business_data['industry'] = agent_result['business_type']
        elif domain:
            # Fall back to domain-based industry inference
            business_data['industry'] = self.infer_industry_from_domain(domain)

        # Add additional contact information if available
        contact_info = agent_result.get('contact_info', {})
        if contact_info.get('phones'):
            business_data['phone'] = contact_info['phones'][0]  # Take first phone number

        # Store agent metadata
        business_data['agent_confidence'] = agent_result.get('identification_confidence', 0.0)
        business_data['agent_tools_used'] = ','.join(agent_result.get('tools_used', []))

        return business_data

    def _create_basic_business_data(self, email_data: EmailData, normalized_name: str) -> Dict[str, Any]:
        """Create basic business data dictionary without agent enhancement.

        Args:
            email_data: Email data
            normalized_name: Normalized business name

        Returns:
            Dictionary containing basic business entity data
        """
        # Extract domain from sender email
        domain = self.extract_domain_from_email(email_data.from_field)

        # Infer industry
        industry = self.infer_industry_from_domain(domain) if domain else None

        # Create business entity data
        business_data = {
            'domain': domain,
            'industry': industry,
            'contact_email': email_data.from_field,
            'description': f"Business entity identified from email communication"
        }

        # Try to extract website from email body
        if email_data.body:
            website = self._extract_website_from_text(email_data.body, domain)
            if website:
                business_data['website'] = website

        return business_data

    @log_execution_time
    def create_business_profile_from_email(self,
                                         business_name: str,
                                         email_data: EmailData) -> BusinessEntity:
        """Create a business profile from email data.

        Args:
            business_name: Name of the business
            email_data: Email data containing business information

        Returns:
            Created BusinessEntity

        Raises:
            BusinessManagerError: If business creation fails
        """
        try:
            # Normalize business name
            normalized_name = self.normalize_business_name(business_name)

            if not normalized_name:
                raise BusinessManagerError(
                    "Cannot create business with empty name",
                    business_name=business_name,
                    operation="create_profile"
                )

            # Use agent for enhanced business identification if enabled
            if self.enable_agent_identification and self.business_agent:
                try:
                    agent_result = self.business_agent.identify_business(
                        from_field=email_data.from_field,
                        subject=email_data.subject,
                        body=email_data.body,
                        additional_context=f"Business name: {normalized_name}"
                    )

                    # Use agent results to enhance business data
                    business_data = self._create_business_data_from_agent_result(
                        agent_result, email_data, normalized_name
                    )

                except Exception as e:
                    self.logger.warning(f"Agent identification failed, falling back to basic extraction: {e}")
                    # Fall back to basic extraction
                    business_data = self._create_basic_business_data(email_data, normalized_name)
            else:
                # Use basic extraction
                business_data = self._create_basic_business_data(email_data, normalized_name)

            self.logger.info("Creating business profile", extra={
                'business_name': normalized_name,
                'domain': business_data.get('domain'),
                'industry': business_data.get('industry'),
                'website': business_data.get('website')
            })
            return self.db.create_business_entity(normalized_name, **business_data)

        except Exception as e:
            error = handle_exception(
                self.logger,
                e,
                f"Creating business profile for {business_name}"
            )
            if isinstance(error, BusinessManagerError):
                raise error
            raise BusinessManagerError(
                f"Failed to create business profile: {e}",
                business_name=business_name,
                operation="create_profile"
            )
    
    def _extract_website_from_text(self, text: str, exclude_domain: str = None) -> Optional[str]:
        """Extract website URL from text content."""
        try:
            # Pattern to match URLs
            url_pattern = r'https?://(?:www\.)?([a-zA-Z0-9-]+\.[a-zA-Z]{2,})'
            urls = re.findall(url_pattern, text)
            
            if urls:
                # Use the first URL found that's different from the email domain
                for url_domain in urls:
                    if not exclude_domain or url_domain.lower() != exclude_domain.lower():
                        return f"https://{url_domain}"
            
            return None
            
        except Exception as e:
            self.logger.warning(f"Error extracting website from text: {e}")
            return None
    
    @log_execution_time
    def get_or_create_business_entity(self, 
                                    business_name: str, 
                                    email_data: EmailData) -> BusinessEntity:
        """Get existing business entity or create new one.
        
        Args:
            business_name: Name of the business
            email_data: Email data for business creation
            
        Returns:
            BusinessEntity (existing or newly created)
            
        Raises:
            BusinessManagerError: If operation fails
        """
        try:
            normalized_name = self.normalize_business_name(business_name)
            
            if not normalized_name:
                raise BusinessManagerError(
                    "Cannot process business with empty name",
                    business_name=business_name,
                    operation="get_or_create"
                )
            
            # Try to find existing business
            existing_business = self.db.get_business_entity_by_name(normalized_name)
            
            if existing_business:
                self.logger.debug(f"Found existing business entity: {normalized_name}")
                return existing_business
            
            # Create new business entity
            self.logger.info(f"Creating new business entity: {normalized_name}")
            business_entity = self.create_business_profile_from_email(normalized_name, email_data)

            # Automatically scrape privacy policy if enabled
            if self.enable_privacy_scraping and business_entity.domain:
                try:
                    self._scrape_and_store_privacy_policy(business_entity)
                except Exception as e:
                    self.logger.warning(f"Failed to scrape privacy policy for {business_entity.name}: {e}")
                    # Don't fail business creation if privacy scraping fails

            return business_entity
            
        except Exception as e:
            error = handle_exception(
                self.logger, 
                e, 
                f"Getting or creating business entity for {business_name}"
            )
            if isinstance(error, BusinessManagerError):
                raise error
            raise BusinessManagerError(
                f"Failed to get or create business entity: {e}",
                business_name=business_name,
                operation="get_or_create"
            )
    
    @log_execution_time
    def record_user_business_interaction(self,
                                       user: User,
                                       business_entity: BusinessEntity,
                                       email_data: EmailData,
                                       category: str,
                                       email_file_path: str = None,
                                       template_id: str = None,
                                       similarity_score: float = None) -> UserBusinessInteraction:
        """Record a user-business interaction.
        
        Args:
            user: User who received the email
            business_entity: Business that sent the email
            email_data: Email content data
            category: Email category
            email_file_path: Path to the email file
            template_id: ID of the email template
            similarity_score: Similarity score if matched to existing template
            
        Returns:
            Created UserBusinessInteraction
            
        Raises:
            BusinessManagerError: If recording fails
        """
        try:
            # Get email category ID
            email_category = self.db.get_email_category_by_name(category)
            email_category_id = email_category.id if email_category else None
            
            # Parse email date
            email_date = self._parse_email_date(email_data.date)
            
            # Create interaction record
            interaction = self.db.create_user_business_interaction(
                user_id=user.id,
                business_entity_id=business_entity.id,
                email_category_id=email_category_id,
                email_subject=email_data.subject[:500] if email_data.subject else None,
                email_from=email_data.from_field,
                email_date=email_date,
                email_file_path=email_file_path,
                template_id=template_id,
                similarity_score=similarity_score
            )
            
            self.logger.info(f"Recorded interaction: user {user.id} <-> business {business_entity.name}")
            return interaction
            
        except Exception as e:
            error = handle_exception(
                self.logger, 
                e, 
                f"Recording interaction between user {user.id} and business {business_entity.name}"
            )
            if isinstance(error, BusinessManagerError):
                raise error
            raise BusinessManagerError(
                f"Failed to record interaction: {e}",
                business_name=business_entity.name,
                operation="record_interaction"
            )
    
    def _parse_email_date(self, date_str: str) -> Optional[datetime]:
        """Parse email date string to datetime object."""
        if not date_str:
            return None

        try:
            from email.utils import parsedate_to_datetime
            return parsedate_to_datetime(date_str)
        except Exception as e:
            self.logger.warning(f"Could not parse email date '{date_str}': {e}")
            return None

    @log_execution_time
    def get_user_business_relationships(self, user_id: int, limit: int = 50) -> List[Dict[str, Any]]:
        """Get business relationships for a user with interaction statistics.

        Args:
            user_id: ID of the user
            limit: Maximum number of relationships to return

        Returns:
            List of business relationships with statistics

        Raises:
            BusinessManagerError: If operation fails
        """
        try:
            with self.db.get_session() as session:
                # Query to get business entities with interaction counts
                from sqlalchemy import func

                query = session.query(
                    BusinessEntity,
                    func.count(UserBusinessInteraction.id).label('interaction_count'),
                    func.max(UserBusinessInteraction.interaction_date).label('last_interaction'),
                    func.min(UserBusinessInteraction.interaction_date).label('first_interaction')
                ).join(
                    UserBusinessInteraction,
                    BusinessEntity.id == UserBusinessInteraction.business_entity_id
                ).filter(
                    UserBusinessInteraction.user_id == user_id
                ).group_by(
                    BusinessEntity.id
                ).order_by(
                    func.count(UserBusinessInteraction.id).desc()
                ).limit(limit)

                results = []
                for business, interaction_count, last_interaction, first_interaction in query.all():
                    results.append({
                        'business_id': business.id,
                        'business_name': business.name,
                        'domain': business.domain,
                        'industry': business.industry,
                        'website': business.website,
                        'contact_email': business.contact_email,
                        'interaction_count': interaction_count,
                        'last_interaction': last_interaction.isoformat() if last_interaction else None,
                        'first_interaction': first_interaction.isoformat() if first_interaction else None
                    })

                self.logger.info(f"Retrieved {len(results)} business relationships for user {user_id}")
                return results

        except Exception as e:
            error = handle_exception(
                self.logger,
                e,
                f"Getting business relationships for user {user_id}"
            )
            if isinstance(error, BusinessManagerError):
                raise error
            raise BusinessManagerError(
                f"Failed to get business relationships: {e}",
                operation="get_relationships"
            )

    @log_execution_time
    def _scrape_and_store_privacy_policy(self, business_entity: BusinessEntity) -> Optional[BusinessPrivacyPolicy]:
        """Scrape and store privacy policy for a business entity.

        Args:
            business_entity: Business entity to scrape privacy policy for

        Returns:
            BusinessPrivacyPolicy if successful, None otherwise
        """
        try:
            if not self.privacy_scraper or not business_entity.domain:
                return None

            self.logger.info(f"Scraping privacy policy for {business_entity.name} ({business_entity.domain})")

            # Check if privacy policy already exists
            existing_policy = self.db.get_business_privacy_policy(business_entity.id)
            if existing_policy:
                self.logger.info(f"Privacy policy already exists for {business_entity.name}")
                return existing_policy

            # Scrape privacy policy
            privacy_data = self.privacy_scraper.scrape_business_privacy_policy(
                business_entity.name,
                business_entity.domain
            )

            if not privacy_data:
                # Create a record indicating scraping failed
                policy_record = self.db.create_business_privacy_policy(
                    business_entity_id=business_entity.id,
                    privacy_policy_url="",
                    scraping_status="failed",
                    scraping_error="No privacy policy found or scraping failed"
                )
                return policy_record

            # Store privacy policy data
            policy_record = self._store_privacy_policy_data(business_entity.id, privacy_data)

            self.logger.info(f"Successfully scraped and stored privacy policy for {business_entity.name}")
            return policy_record

        except Exception as e:
            error = handle_exception(
                self.logger,
                e,
                f"Scraping privacy policy for {business_entity.name}"
            )

            # Create a record indicating scraping failed
            try:
                policy_record = self.db.create_business_privacy_policy(
                    business_entity_id=business_entity.id,
                    privacy_policy_url="",
                    scraping_status="error",
                    scraping_error=str(error)
                )
                return policy_record
            except Exception as db_error:
                self.logger.error(f"Failed to create error record for privacy policy: {db_error}")
                return None

    def _store_privacy_policy_data(self, business_entity_id: int, privacy_data: PrivacyPolicyData) -> BusinessPrivacyPolicy:
        """Store privacy policy data in the database.

        Args:
            business_entity_id: ID of the business entity
            privacy_data: Scraped privacy policy data

        Returns:
            Created BusinessPrivacyPolicy record
        """
        try:
            # Create privacy policy record
            policy_record = self.db.create_business_privacy_policy(
                business_entity_id=business_entity_id,
                privacy_policy_url=privacy_data.url,
                last_scraped_at=datetime.now(),
                last_updated_date=privacy_data.last_updated,
                policy_text_excerpt=privacy_data.policy_text,
                scraping_status="success",
                contact_email=privacy_data.contact_info.email,
                contact_phone=privacy_data.contact_info.phone,
                contact_address=privacy_data.contact_info.address,
                contact_website=privacy_data.contact_info.website
            )

            # Store data collection practices
            for practice in privacy_data.data_practices:
                self.db.create_privacy_data_practice(
                    privacy_policy_id=policy_record.id,
                    category=practice.category,
                    description=practice.description,
                    purpose=practice.purpose,
                    retention_period=practice.retention_period,
                    third_party_sharing=practice.third_party_sharing
                )

            self.logger.info(f"Stored privacy policy with {len(privacy_data.data_practices)} data practices")
            return policy_record

        except Exception as e:
            error = handle_exception(
                self.logger,
                e,
                f"Storing privacy policy data for business {business_entity_id}"
            )
            raise BusinessManagerError(
                f"Failed to store privacy policy data: {e}",
                operation="store_privacy_data"
            )

    @log_execution_time
    def get_business_privacy_policy(self, business_entity_id: int) -> Optional[Dict[str, Any]]:
        """Get privacy policy information for a business entity.

        Args:
            business_entity_id: ID of the business entity

        Returns:
            Privacy policy information dictionary or None
        """
        try:
            policy = self.db.get_business_privacy_policy(business_entity_id)
            if not policy:
                return None

            # Get data practices
            practices = self.db.get_privacy_data_practices(policy.id)

            return {
                'id': policy.id,
                'business_entity_id': policy.business_entity_id,
                'privacy_policy_url': policy.privacy_policy_url,
                'last_scraped_at': policy.last_scraped_at.isoformat() if policy.last_scraped_at else None,
                'last_updated_date': policy.last_updated_date,
                'scraping_status': policy.scraping_status,
                'scraping_error': policy.scraping_error,
                'contact_info': {
                    'email': policy.contact_email,
                    'phone': policy.contact_phone,
                    'address': policy.contact_address,
                    'website': policy.contact_website
                },
                'data_practices': [
                    {
                        'id': practice.id,
                        'category': practice.category,
                        'description': practice.description,
                        'purpose': practice.purpose,
                        'retention_period': practice.retention_period,
                        'third_party_sharing': practice.third_party_sharing
                    }
                    for practice in practices
                ]
            }

        except Exception as e:
            error = handle_exception(
                self.logger,
                e,
                f"Getting privacy policy for business {business_entity_id}"
            )
            raise BusinessManagerError(
                f"Failed to get privacy policy: {e}",
                operation="get_privacy_policy"
            )

    @log_execution_time
    def scrape_privacy_policy_manually(self, business_entity_id: int) -> Optional[BusinessPrivacyPolicy]:
        """Manually trigger privacy policy scraping for a business entity.

        Args:
            business_entity_id: ID of the business entity

        Returns:
            BusinessPrivacyPolicy if successful, None otherwise
        """
        try:
            # Get business entity
            with self.db.get_session() as session:
                business_entity = session.query(BusinessEntity).filter(
                    BusinessEntity.id == business_entity_id
                ).first()

                if not business_entity:
                    raise BusinessManagerError(
                        f"Business entity not found: {business_entity_id}",
                        operation="manual_scrape"
                    )

                return self._scrape_and_store_privacy_policy(business_entity)

        except Exception as e:
            error = handle_exception(
                self.logger,
                e,
                f"Manual privacy policy scraping for business {business_entity_id}"
            )
            if isinstance(error, BusinessManagerError):
                raise error
            raise BusinessManagerError(
                f"Failed to manually scrape privacy policy: {e}",
                operation="manual_scrape"
            )

    def get_enhanced_business_identification(self, email_data: EmailData) -> Dict[str, Any]:
        """Get enhanced business identification using the LangChain agent.

        Args:
            email_data: Email data to analyze

        Returns:
            Dictionary containing comprehensive business information

        Raises:
            BusinessManagerError: If identification fails
        """
        try:
            if not self.enable_agent_identification or not self.business_agent:
                raise BusinessManagerError(
                    "Agent identification is not enabled",
                    operation="enhanced_identification"
                )

            self.logger.info(f"Getting enhanced business identification for: {email_data.from_field}")

            result = self.business_agent.identify_business(
                from_field=email_data.from_field,
                subject=email_data.subject,
                body=email_data.body
            )

            self.logger.info("Enhanced business identification completed", extra={
                'business_name': result.get('business_name'),
                'confidence': result.get('identification_confidence'),
                'tools_used': result.get('tools_used', [])
            })

            return result

        except Exception as e:
            error = handle_exception(
                self.logger,
                e,
                f"Enhanced business identification for {email_data.from_field}"
            )
            if isinstance(error, BusinessManagerError):
                raise error
            raise BusinessManagerError(
                f"Failed to get enhanced business identification: {e}",
                operation="enhanced_identification"
            )

    def close(self):
        """Close the privacy scraper session."""
        if self.privacy_scraper:
            self.privacy_scraper.close()
            self.logger.info("Privacy scraper session closed")


# Global business manager instance
_business_manager = None

def get_business_manager() -> BusinessProfileManager:
    """Get the global business manager instance."""
    global _business_manager
    if _business_manager is None:
        _business_manager = BusinessProfileManager()
    return _business_manager

def reset_business_manager():
    """Reset the global business manager instance."""
    global _business_manager
    _business_manager = None
